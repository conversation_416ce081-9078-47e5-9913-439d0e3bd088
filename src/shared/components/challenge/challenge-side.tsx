import { useChallengeDetail } from '../../../apps/challenges/hooks/use-challenge-detail';
import { ChallengeListElement } from '../../../apps/challenges/types';
import { Col, ProgressBar, Row } from 'solid-bootstrap';
import { For } from 'solid-js';
import logo from '../../../assets/media/icons/blank.png';
import '../../../assets/css/challenge-side.css';

const ChallengeSide = ({ challenge }: { challenge: ChallengeListElement }) => {
  const {
    renderGoal,
    renderEvidenceType,
    renderChallengeProgressType,
    renderStartDate,
    renderEndDate,
    handleImageLoadError,
  } = useChallengeDetail();

  return (
    <>
      <div class="challenge-detail-card px-sx-12 px-sm-12 px-md-5 px-lg-5 px-xl-5 px-xxl-12 px-12 py-8">
        <div class="mb-8">
          <h3>Acerca de tu meta</h3>
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-3">
            Nombre de la meta
          </h5>
          <h4 class="challenge-detail-card-subtitle">🏁 {challenge.name}</h4>
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-3">
            Descripción
          </h5>
          <h4 class="challenge-detail-card-subtitle">
            💬 {challenge.description || '...'}
          </h4>
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-4">
            Recompensa
          </h5>
          <h4 class="challenge-detail-card-name font-weight-bold">
            🏆 {challenge.reward}
          </h4>
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-3">
            Meta {challenge.progressType === 'HABIT' ? '(Diaria)' : ''}
          </h5>
          {renderGoal(challenge)}
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-3">
            Tipo de Meta
          </h5>
          {renderChallengeProgressType(challenge)}
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-3">Evidencia</h5>
          {renderEvidenceType(challenge)}
        </div>
        <div class="mb-8">
          <h5 class="challenge-detail-card-title text-muted mb-4">
            Rango de tiempo de la meta
          </h5>
          <ProgressBar
            class="mt-5 mb-8 w-75"
            variant="primary"
            style={{ height: '8px', background: '#EFEFEF' }}
            now={challenge.timeProgress}
          />
          <Row class="mt-0">
            <Col class="mt-0" sm={12} md={12} lg={6}>
              {renderStartDate(challenge)}
            </Col>
            <Col sm={12} md={12} lg={6}>
              {renderEndDate(challenge)}
            </Col>
          </Row>
        </div>
        {(challenge.participants || []).length > 0 && (
          <div class="mb-10">
            <h5 class="challenge-detail-card-title text-muted mb-5">
              Participantes
            </h5>
            <div class="participant-container my-3">
              <For each={challenge.participants}>
                {(participant) => (
                  <Row>
                    <Col class="my-3" sm={10} md={10} lg={10}>
                      <div
                        class="p-0 m-0"
                        style={'display: flex; align-items: center;'}
                      >
                        <img
                          src={logo}
                          width="25"
                          height="25"
                          class="mr-3 "
                          onError={handleImageLoadError}
                          style={
                            'align-self: center; border-radius: 50%; margin-right: 10px'
                          }
                        />
                        {'    ' + participant.name}
                      </div>
                      <small class="ms-12 text-muted">
                        {participant.participantStatusHuman}
                      </small>
                    </Col>
                  </Row>
                )}
              </For>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
export default ChallengeSide;
