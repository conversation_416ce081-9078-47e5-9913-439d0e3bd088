import UserSearchBar, { User } from '../user/user-search-bar';
import { Form } from 'solid-bootstrap';
import { Accessor, Setter, Show, createEffect, createSignal } from 'solid-js';
import { createStore } from 'solid-js/store';
import { getCurrentUserData } from '../../services/user/user-session-management';
import { User as UserEntity, UserCountry } from '../../../entities/User';

export type ParticipanList = {
  activeUserList: Accessor<UserEntity[]>;
  selectedUser: Accessor<User[]>;
  setSelectedUser: Setter<User[]>;
};
export const ParticipanList = (props: ParticipanList) => {
  const { userType } = getCurrentUserData();
  const [selectedCountries, setSelectedCountries] = createSignal<UserCountry[]>(
    []
  );
  const [control, setControl] = createStore<{
    allSelected: boolean;
    init: boolean;
  }>({
    allSelected: false,
    init: true,
  });
  // Convert UserEntity to User type
  const convertUserEntityToUser = (userEntity: UserEntity): User => ({
    id: userEntity.id,
    name: userEntity.name,
    email: userEntity.email,
    profilePicture: userEntity.profilePicture,
    country: userEntity.country,
  });
  // Get filtered users by country for "select all"
  const getFilteredUsersForSelectAll = () => {
    let filteredUsers = props.activeUserList();
    // Filter out current session user
    const currentUser = getCurrentUserData();
    if (currentUser.id) {
      filteredUsers = filteredUsers.filter(
        (user) => user.id !== currentUser.id
      );
    }
    // Filter by selected countries if any are selected
    if (selectedCountries().length > 0) {
      filteredUsers = filteredUsers.filter((user) =>
        selectedCountries().includes(user.country)
      );
    }

    return filteredUsers.map(convertUserEntityToUser);
  };
  createEffect(() => {
    if (control.allSelected === true) {
      // Select all users (filtered by country if applicable)
      const usersToSelect = getFilteredUsersForSelectAll();
      props.setSelectedUser(usersToSelect);
    } else if (control.allSelected === false && control.init === true) {
      props.setSelectedUser(props.selectedUser());
    } else {
      props.setSelectedUser([]);
    }
  });

  return (
    <>
      <Form.Label class="mb-8" for="price">
        Participantes:
      </Form.Label>
      <Show when={userType === 'ADMIN'}>
        <Form.Check
          type="switch"
          id="custom-switch"
          label={`Seleccionar todos los participantes${
            selectedCountries().length > 0 ? ' (filtrados por país)' : ''
          }`}
          checked={control.allSelected}
          onChange={() =>
            setControl(() => ({
              allSelected: !control.allSelected,
              init: false,
            }))
          }
        />
      </Show>
      <UserSearchBar
        userList={props.activeUserList}
        selectedUser={props.selectedUser}
        setSelectedUser={props.setSelectedUser}
        isDisabled={control.allSelected}
        enableCountryFilter={true}
        selectedCountries={selectedCountries}
        setSelectedCountries={setSelectedCountries}
        placeholder="Buscar participantes por nombre o email"
      />
    </>
  );
};
